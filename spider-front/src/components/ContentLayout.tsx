/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2025-08-20 22:54:41
 * @LastEditors: zhouming
 * @LastEditTime: 2025-08-20 23:32:17
 * @FilePath: /spider-front/src/components/ContentLayout.tsx
 */
'use client'

import { useState } from 'react'
import { Layout, Menu, Breadcrumb } from 'antd'
import { MenuFoldOutlined, MenuUnfoldOutlined, SearchOutlined, FileSearchOutlined, DatabaseOutlined } from '@ant-design/icons'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const { Header, Sider, Content } = Layout

interface ContentLayoutProps {
  children: React.ReactNode
}

export default function ContentLayout({ children }: ContentLayoutProps) {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()

  const menuItems = [
    {
      key: '/content/corpsearch',
      icon: <FileSearchOutlined />,
      label: <Link href="/content/corpsearch">检索信息</Link>,
    },
    {
      key: '/content/homepage',
      icon: <SearchOutlined />,
      label: <Link href="/content/homepage">搜索公司</Link>,
    },
    {
      key: '/content/database',
      icon: <DatabaseOutlined />,
      label: <Link href="/content/database">已录入</Link>,
    },
  ]

  return (
    <div style={{
      background: '#f5f7f9',
      overflow: 'hidden',
      minHeight: '100vh'
    }}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{ padding: 0, background: '#001529', margin: 0 }}>
          {/* Header content can be added here if needed */}
        </Header>
        <Layout>
          <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            style={{ background: '#fff' }}
            width={200}
            collapsedWidth={78}
          >
            <Menu
              mode="inline"
              selectedKeys={[pathname]}
              style={{ height: '100%', borderRight: 0 }}
              items={menuItems}
            />
          </Sider>
          <Layout style={{ padding: '0 0px 24px' }}>
            <Content style={{ padding: '0 50px' }}>
              <div style={{ margin: '20px 0', display: 'flex', alignItems: 'center' }}>
                <div
                  onClick={() => setCollapsed(!collapsed)}
                  style={{
                    padding: '0 10px 0 0',
                    cursor: 'pointer',
                    display: 'inline-block',
                    fontSize: '18px'
                  }}
                >
                  {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                </div>
                <Breadcrumb
                  items={[
                    {
                      title: <Link href="/content/homepage">首页</Link>,
                    },
                    {
                      title: '检索',
                    },
                  ]}
                />
              </div>
              {children}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </div>
  )
}
