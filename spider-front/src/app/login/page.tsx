'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Layout, Menu, Row, Col, Card, Form, Input, Button, Checkbox } from 'antd'
import type { FormProps } from 'antd'

const { Header } = Layout

type FieldType = {
  email?: string
  password?: string
  remember?: boolean
}

export default function Login() {
  const router = useRouter()
  const [form] = Form.useForm()

  const onFinish: FormProps<FieldType>['onFinish'] = (values) => {
    console.log('Success:', values)
    // Redirect to content page
    router.push('/content/corpsearch')
  }

  const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onReset = () => {
    form.resetFields()
  }

  return (
    <div>
      <Header>
        <Menu mode="horizontal" theme="dark" />
      </Header>
      <Row justify="center" style={{ marginTop: '8%' }}>
        <Col span={6} offset={9}>
          <Card>
            <h2 style={{ marginBottom: '16px' }}>登录</h2>
            <Form
              form={form}
              name="login"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              autoComplete="off"
            >
              <Form.Item<FieldType>
                label="Email address"
                name="email"
                rules={[
                  { required: true, message: 'Please input your email!' },
                  { type: 'email', message: 'Please enter a valid email!' }
                ]}
              >
                <Input placeholder="Enter email" />
              </Form.Item>

              <Form.Item<FieldType>
                label="Password"
                name="password"
                rules={[{ required: true, message: 'Please input your password!' }]}
              >
                <Input.Password placeholder="Enter password" />
              </Form.Item>

              <Form.Item<FieldType>
                name="remember"
                valuePropName="checked"
                wrapperCol={{ offset: 8, span: 16 }}
              >
                <Checkbox>Remember me</Checkbox>
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Button type="primary" htmlType="submit" style={{ marginRight: '8px' }}>
                      提交
                    </Button>
                    <Button danger onClick={onReset}>
                      重置
                    </Button>
                  </div>
                  <div>请直接点提交</div>
                </div>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  )
}
