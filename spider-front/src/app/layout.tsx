import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { AppProvider } from '@/contexts/AppContext'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Spider Front',
  description: 'Company search application',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <AppProvider>
          <ConfigProvider locale={zhCN}>
            {children}
          </ConfigProvider>
        </AppProvider>
      </body>
    </html>
  )
}
