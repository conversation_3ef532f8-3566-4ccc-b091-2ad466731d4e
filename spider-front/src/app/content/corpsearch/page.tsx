'use client'

import { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Form, 
  Input, 
  DatePicker, 
  InputNumber, 
  Select, 
  Button, 
  Tag, 
  Radio, 
  Table, 
  Switch, 
  Pagination,
  Modal,
  message
} from 'antd'
import { DownloadOutlined, CloseOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { exportTableToExcel } from '@/utils/excel'

const { RangePicker } = DatePicker
const { Option } = Select

interface SearchForm {
  corpName: string
  handDate: [string, string] | null
  kilometerSmall: number | null
  kilometerBig: number | null
  constructStatus: string | null
  endDate: [string, string] | null
  priceSmall: number | null
  priceBig: number | null
  keyWords: string
  keyType: 'and' | 'or'
}

interface DataItem {
  key: string
  corpName: string
  projectName: string
  technologyGrade: string
  province: string
  beginDate: string
  handDate: string
  endDate: string
  kilometer: string
  contractPrice: string
  settlementPrice: string
  remark: string
}

export default function CorpSearch() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<DataItem[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [showIndex, setShowIndex] = useState(false)
  const [keyWordsStr, setKeyWordsStr] = useState('')
  const [keywordsArray, setKeywordsArray] = useState<string[]>([])
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([])

  const constructStatusOptions = [
    { label: '总包已建', value: '总包已建' },
    { label: '总包在建', value: '总包在建' }
  ]

  // Watch keyWordsStr changes
  useEffect(() => {
    if (keyWordsStr === '') {
      setKeywordsArray([])
      return
    }
    setKeywordsArray(keyWordsStr.split(' ').filter(word => word.trim() !== ''))
  }, [keyWordsStr])

  const columns: ColumnsType<DataItem> = [
    ...(showIndex ? [{
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: DataItem, index: number) => (pageIndex - 1) * pageSize + index + 1
    }] : []),
    {
      title: '企业名称',
      dataIndex: 'corpName',
      key: 'corpName',
      sorter: true,
    },
    {
      title: '工程名称',
      dataIndex: 'projectName',
      key: 'projectName',
    },
    {
      title: '技术等级',
      dataIndex: 'technologyGrade',
      key: 'technologyGrade',
      filters: [
        { text: '高速公路', value: '高速公路' },
        { text: '一级公路', value: '一级公路' },
        { text: '二级公路', value: '二级公路' },
        { text: '三级公路', value: '三级公路' },
        { text: '特大型桥梁工程', value: '特大型桥梁工程' },
        { text: '其他工程', value: '其他工程' },
      ],
      onFilter: (value, record) => record.technologyGrade === value,
    },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
    },
    {
      title: '开工日期',
      dataIndex: 'beginDate',
      key: 'beginDate',
      sorter: true,
    },
    {
      title: '交工日期',
      dataIndex: 'handDate',
      key: 'handDate',
      sorter: true,
    },
    {
      title: '竣工日期',
      dataIndex: 'endDate',
      key: 'endDate',
      sorter: true,
    },
    {
      title: '公里数(单位:m)',
      dataIndex: 'kilometer',
      key: 'kilometer',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record, index) => (
        <Button type="primary" onClick={() => showInfo(record, index)}>
          查看更多
        </Button>
      ),
    },
  ]

  const showInfo = (record: DataItem, index: number) => {
    Modal.info({
      title: '详细信息',
      content: (
        <div>
          <p><strong>企业名称：</strong>{record.corpName}</p>
          <p><strong>工程名称：</strong>{record.projectName}</p>
          <p><strong>技术等级：</strong>{record.technologyGrade}</p>
          <p><strong>省份：</strong>{record.province}</p>
          <p><strong>合同金额：</strong>{record.contractPrice}</p>
          <p><strong>结算金额：</strong>{record.settlementPrice}</p>
          <p><strong>主要业绩：</strong>{record.remark}</p>
        </div>
      ),
    })
  }

  const handleTagClose = (tag: string) => {
    const newArray = keywordsArray.filter(item => item !== tag)
    setKeywordsArray(newArray)
    setKeyWordsStr(newArray.join(' '))
  }

  const onSubmit = async (values: SearchForm) => {
    setLoading(true)
    try {
      // Simulate API call
      const searchParams = {
        ...values,
        keyWords: keywordsArray.join(','),
        handDate: values.handDate ? values.handDate.join(',') : '',
        endDate: values.endDate ? values.endDate.join(',') : '',
      }
      
      console.log('Search params:', searchParams)
      
      // Mock data for demonstration
      const mockData: DataItem[] = Array.from({ length: 50 }, (_, i) => ({
        key: `${i}`,
        corpName: `企业${i + 1}`,
        projectName: `工程项目${i + 1}`,
        technologyGrade: ['高速公路', '一级公路', '二级公路'][i % 3],
        province: ['北京', '上海', '广东', '浙江'][i % 4],
        beginDate: '2020-01-01',
        handDate: '2021-01-01',
        endDate: '2021-06-01',
        kilometer: `${(i + 1) * 10}`,
        contractPrice: `${(i + 1) * 1000}万`,
        settlementPrice: `${(i + 1) * 950}万`,
        remark: `项目${i + 1}的主要业绩描述`
      }))
      
      setData(mockData)
      setTotal(mockData.length)
      setPageIndex(1)
      message.success('搜索完成')
    } catch (error) {
      message.error('搜索失败')
    } finally {
      setLoading(false)
    }
  }

  const onReset = () => {
    form.resetFields()
    setKeyWordsStr('')
    setKeywordsArray([])
    setData([])
    setTotal(0)
    setPageIndex(1)
  }

  const closeExpand = () => {
    setExpandedRowKeys([])
  }

  const downloadSelected = () => {
    try {
      if (data.length === 0) {
        message.warning('没有数据可以下载')
        return
      }
      exportTableToExcel(data, '企业搜索结果.xlsx')
      message.success('下载成功')
    } catch (error) {
      message.error('下载失败')
    }
  }

  const handlePageChange = (page: number, size: number) => {
    setPageIndex(page)
    setPageSize(size)
  }

  return (
    <div style={{ minHeight: 200 }}>
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmit}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="企业名称:" name="corpName">
                <Input placeholder="企业完整全称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="交工起止:" name="handDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="完成里程数(单位：km):">
                <Input.Group compact>
                  <Form.Item name="kilometerSmall" noStyle>
                    <InputNumber placeholder="最低值" style={{ width: '45%' }} />
                  </Form.Item>
                  <Form.Item name="kilometerBig" noStyle>
                    <InputNumber placeholder="最高值" style={{ width: '45%', marginLeft: '10%' }} />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="修建状态:" name="constructStatus">
                <Select placeholder="--请选择--" allowClear>
                  {constructStatusOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="竣工起止:" name="endDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="合同金额(单位：1000):">
                <Input.Group compact>
                  <Form.Item name="priceSmall" noStyle>
                    <InputNumber placeholder="最低值" style={{ width: '45%' }} />
                  </Form.Item>
                  <Form.Item name="priceBig" noStyle>
                    <InputNumber placeholder="最高值" style={{ width: '45%', marginLeft: '10%' }} />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="关键词:">
                <Input
                  value={keyWordsStr}
                  onChange={(e) => setKeyWordsStr(e.target.value)}
                  placeholder="(空格分隔)桥梁 路基..."
                />
                <div style={{ marginTop: 8 }}>
                  {keywordsArray.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      color="success"
                      onClose={() => handleTagClose(tag)}
                    >
                      {tag}
                    </Tag>
                  ))}
                  {keywordsArray.length > 0 && (
                    <Form.Item name="keyType" style={{ display: 'inline-block', marginLeft: 16 }}>
                      <Radio.Group>
                        <Radio value="and">AND</Radio>
                        <Radio value="or">OR</Radio>
                      </Radio.Group>
                    </Form.Item>
                  )}
                </div>
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Button type="primary" htmlType="submit" loading={loading} style={{ marginRight: 8 }}>
                检索
              </Button>
              <Button onClick={onReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      <Row style={{ margin: '12px 0' }}>
        <Col span={4}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            行号
            <Switch
              checked={showIndex}
              onChange={setShowIndex}
              style={{ marginLeft: 8 }}
            />
          </div>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: setExpandedRowKeys,
          expandedRowRender: (record) => (
            <div>
              <p><strong>合同金额：</strong>{record.contractPrice}</p>
              <p><strong>结算金额：</strong>{record.settlementPrice}</p>
              <p><strong>主要业绩：</strong>{record.remark}</p>
            </div>
          ),
        }}
      />

      <Row style={{ marginTop: 16 }}>
        <Col>
          <Button type="primary" icon={<CloseOutlined />} onClick={closeExpand} style={{ marginRight: 8 }}>
            收起扩展行
          </Button>
          <Button type="primary" icon={<DownloadOutlined />} onClick={downloadSelected}>
            下载当前页
          </Button>
        </Col>
      </Row>

      <div style={{ textAlign: 'center', marginTop: 16 }}>
        <Pagination
          current={pageIndex}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} items`}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>
    </div>
  )
}
