import axios, { AxiosResponse, AxiosError } from 'axios'
import { message } from 'antd'

// Base URL configuration
const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://10.8.0.1:18084'

// Create axios instance
const httpClient = axios.create({
  baseURL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
})

// Token management
const getToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token')
  }
  return null
}

const setToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('token', token)
  }
}

const removeToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('token')
    localStorage.removeItem('expireTime')
  }
}

const getExpireTime = (): number => {
  if (typeof window !== 'undefined') {
    return Number(localStorage.getItem('expireTime')) || 0
  }
  return 0
}

const setExpireTime = (expireTime: number): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('expireTime', expireTime.toString())
  }
}

// Request interceptor
httpClient.interceptors.request.use(
  (config) => {
    // Add token to headers if available
    const token = getToken()
    if (token) {
      config.headers.token = token
    }

    // Check token expiration (except for login requests)
    if (config.url && config.url.indexOf('login') < 0) {
      const expireTime = getExpireTime()
      const lessTime = expireTime - Date.now()
      
      if (lessTime <= 0 && token) {
        message.warning('登录已过期，请重新登录')
        removeToken()
        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
      }
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// Response interceptor
httpClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Handle login response
    if (response.config.url && response.config.url.indexOf('login') >= 0) {
      const { token, expireTime, code } = response.data
      if (code === '200' && token) {
        setToken(token)
        setExpireTime(expireTime)
      }
    }

    // Handle error status
    if (response.data.status !== 'ok' && response.data.msg) {
      message.warning(response.data.msg)
    }

    return response
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      message.error('未授权，请重新登录')
      removeToken()
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      }
    } else if (error.message) {
      message.error(error.message)
    }
    return Promise.reject(error)
  }
)

// HTTP methods
export const http = {
  get: <T = any>(url: string, params?: any): Promise<AxiosResponse<T>> => {
    return httpClient.get(url, { params })
  },
  
  post: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => {
    return httpClient.post(url, data)
  },
  
  put: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => {
    return httpClient.put(url, data)
  },
  
  patch: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => {
    return httpClient.patch(url, data)
  },
  
  delete: <T = any>(url: string): Promise<AxiosResponse<T>> => {
    return httpClient.delete(url)
  },
}

export default http
