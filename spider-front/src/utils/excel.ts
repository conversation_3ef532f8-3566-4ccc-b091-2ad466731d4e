import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

/**
 * Export JSON data to Excel file
 * @param headers - Array of header names
 * @param data - Array of data objects
 * @param filename - Name of the exported file
 */
export function exportJsonToExcel(
  headers: string[],
  data: any[][],
  filename: string = 'export.xlsx'
) {
  try {
    // Create a new workbook
    const wb = XLSX.utils.book_new()
    
    // Create worksheet data with headers
    const wsData = [headers, ...data]
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(wsData)
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
    
    // Generate Excel file buffer
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    
    // Create blob and save file
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    saveAs(blob, filename)
  } catch (error) {
    console.error('Error exporting to Excel:', error)
    throw new Error('导出Excel文件失败')
  }
}

/**
 * Format JSON data for Excel export
 * @param filterVal - Array of object keys to extract
 * @param jsonData - Array of data objects
 * @returns Formatted 2D array
 */
export function formatJsonForExcel(filterVal: string[], jsonData: any[]): any[][] {
  return jsonData.map(item => 
    filterVal.map(key => {
      const value = item[key]
      return value !== undefined && value !== null ? value : ''
    })
  )
}

/**
 * Export table data to Excel with predefined format
 * @param data - Array of data objects
 * @param filename - Name of the exported file
 */
export function exportTableToExcel(data: any[], filename: string = '导出数据.xlsx') {
  const headers = [
    '公司名',
    '工程名称', 
    '技术等级',
    '开工日期',
    '合同金额',
    '结算金额',
    '省份',
    '交工日期',
    '竣工日期',
    '主要业绩',
    '公里数(单位：m)'
  ]
  
  const filterKeys = [
    'corpName',
    'projectName',
    'technologyGrade', 
    'beginDate',
    'contractPrice',
    'settlementPrice',
    'province',
    'handDate',
    'endDate',
    'remark',
    'kilometer'
  ]
  
  const formattedData = formatJsonForExcel(filterKeys, data)
  exportJsonToExcel(headers, formattedData, filename)
}
