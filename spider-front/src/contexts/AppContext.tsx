'use client'

import React, { createContext, use<PERSON>ontext, useReducer, ReactNode } from 'react'

// Define the state interface
interface AppState {
  showFooter: boolean
  changableNum: number
  loading: boolean
  user: {
    token: string | null
    expireTime: number | null
  }
}

// Define action types
type AppAction =
  | { type: 'SHOW_FOOTER' }
  | { type: 'HIDE_FOOTER' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'ADD_NUM'; payload: number }
  | { type: 'SET_USER'; payload: { token: string; expireTime: number } }
  | { type: 'CLEAR_USER' }

// Initial state
const initialState: AppState = {
  showFooter: true,
  changableNum: 0,
  loading: false,
  user: {
    token: null,
    expireTime: null,
  },
}

// Reducer function
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SHOW_FOOTER':
      return { ...state, showFooter: true }
    
    case 'HIDE_FOOTER':
      return { ...state, showFooter: false }
    
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'ADD_NUM':
      return { ...state, changableNum: state.changableNum + action.payload }
    
    case 'SET_USER':
      return {
        ...state,
        user: {
          token: action.payload.token,
          expireTime: action.payload.expireTime,
        },
      }
    
    case 'CLEAR_USER':
      return {
        ...state,
        user: {
          token: null,
          expireTime: null,
        },
      }
    
    default:
      return state
  }
}

// Context interface
interface AppContextType {
  state: AppState
  dispatch: React.Dispatch<AppAction>
  // Action creators
  showFooter: () => void
  hideFooter: () => void
  setLoading: (loading: boolean) => void
  addNum: (num: number) => void
  setUser: (token: string, expireTime: number) => void
  clearUser: () => void
}

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined)

// Provider component
interface AppProviderProps {
  children: ReactNode
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Action creators
  const showFooter = () => dispatch({ type: 'SHOW_FOOTER' })
  const hideFooter = () => dispatch({ type: 'HIDE_FOOTER' })
  const setLoading = (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading })
  const addNum = (num: number) => dispatch({ type: 'ADD_NUM', payload: num })
  const setUser = (token: string, expireTime: number) => 
    dispatch({ type: 'SET_USER', payload: { token, expireTime } })
  const clearUser = () => dispatch({ type: 'CLEAR_USER' })

  const value: AppContextType = {
    state,
    dispatch,
    showFooter,
    hideFooter,
    setLoading,
    addNum,
    setUser,
    clearUser,
  }

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>
}

// Custom hook to use the context
export function useAppContext() {
  const context = useContext(AppContext)
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}

export default AppContext
