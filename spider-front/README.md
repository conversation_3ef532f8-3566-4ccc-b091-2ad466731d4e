# Spider Front - Next.js Refactored Version

This is a refactored version of the original Vue.js spider-front application, now built with Next.js, React, and TypeScript.

## Features

- **Modern Stack**: Built with Next.js 14, React 18, and TypeScript
- **UI Components**: Uses Ant Design for consistent and professional UI
- **State Management**: React Context API for global state management
- **HTTP Client**: Axios with interceptors for API communication
- **Excel Export**: XLSX library for data export functionality
- **Responsive Design**: Mobile-friendly responsive layout

## Project Structure

```
spider-front/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── login/             # Login page
│   │   ├── content/           # Content pages with layout
│   │   │   ├── corpsearch/    # Company search functionality
│   │   │   └── homepage/      # Homepage with navigation
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page (redirects to login)
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable React components
│   │   └── ContentLayout.tsx  # Layout for content pages
│   ├── contexts/              # React Context providers
│   │   └── AppContext.tsx     # Global app state
│   ├── lib/                   # Utility libraries
│   │   └── http.ts           # HTTP client configuration
│   ├── utils/                 # Utility functions
│   │   └── excel.ts          # Excel export utilities
│   └── types/                 # TypeScript type definitions
├── package.json
├── tsconfig.json
├── next.config.js
└── README.md
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   Create a `.env.local` file in the root directory:
   ```
   NEXT_PUBLIC_API_BASE_URL=http://********:18084
   ```

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Key Changes from Vue.js Version

### Framework Migration
- **Vue.js → React**: Converted all Vue components to React functional components
- **Vue Router → Next.js App Router**: File-based routing system
- **Vuex → React Context**: State management with Context API

### UI Library Migration
- **iView/Element UI → Ant Design**: Modern React UI component library
- **Bootstrap Vue → Ant Design**: Consistent design system

### Build System
- **Webpack → Next.js**: Zero-config build system with optimizations
- **Babel → SWC**: Faster compilation with Rust-based compiler

### Type Safety
- **JavaScript → TypeScript**: Full type safety and better developer experience

## API Integration

The application connects to the backend API for:
- User authentication
- Company search functionality
- Data export features

Configure the API base URL in the environment variables.

## Deployment

### Build for Production

```bash
npm run build
npm run start
```

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
